import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import {
  DataSource,
  Repository,
  In,
  Not,
  FindOneOptions,
  EntityTarget,
  ObjectLiteral,
  ILike,
} from 'typeorm';
import { ChatAi } from './entities/chatAi.entity';
import { ChatAiDocument } from './entities/document.entity';
import { ChatAiMessage } from './entities/message.entity';
import { ChatAiCreditUsage } from './entities/credit-usage.entity';
import { ChatAiApiTransaction } from './entities/transaction.entity';
import { InjectRepository } from '@nestjs/typeorm';
import {
  CreateChatAiDto,
  UpdateChatAiDto,
  CreateDocumentDto,
  UpdateDocumentDto,
  CreateChatMessageDto,
  ChatQueryDto,
  FetchSingleChatAiDto,
  FetchChatAisDto,
  RemoveChatAiDto,
  UpdateChatAiSettingDto,
  GetChatAiTransactionDto,
} from './dto/chatAi.dto';
import {
  CommonMessage,
  AppMessage,
  TransactionMessage,
  UserMessage,
} from '../CommonMessages/CommonMessages';
import { paginate } from '../utils/common.service';
import { Application } from '../application/entities/application.entity';

@Injectable()
export class ChatAiService {
  private readonly logger = new Logger(ChatAiService.name, {
    timestamp: true,
  });

  constructor(
    @InjectRepository(ChatAi)
    private readonly chatAiRepository: Repository<ChatAi>,
    @InjectRepository(ChatAiDocument)
    private readonly documentRepository: Repository<ChatAiDocument>,
    @InjectRepository(ChatAiMessage)
    private readonly messageRepository: Repository<ChatAiMessage>,
    @InjectRepository(ChatAiCreditUsage)
    private readonly creditUsageRepository: Repository<ChatAiCreditUsage>,
    @InjectRepository(ChatAiApiTransaction)
    private readonly apiTransactionRepository: Repository<ChatAiApiTransaction>,
    @InjectRepository(Application)
    private readonly applicationRepository: Repository<Application>,
    private readonly dataSource: DataSource,
  ) {}

  // ==================== Main ChatAI CRUD Operations ====================

  async setupChatAi(userId: number, payload: CreateChatAiDto) {
    try {
      // Check if app exists and belongs to user
      const app = await this.applicationRepository.findOne({
        where: { id: payload.appId, user: { id: userId } },
      });
      if (!app) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }
      if (!app.isActive) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppIsInactive,
        };
      }

      // Check if ChatAI already exists for this app
      const existingChatAi = await this.chatAiRepository.findOne({
        where: { app: { id: payload.appId } },
      });
      if (existingChatAi) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AlreadySetup('ChatAI'),
        };
      }

      // Create new ChatAI project
      const newChatAi = this.chatAiRepository.create({
        name: payload.name,
        description: payload.description,
        userId: userId.toString(),
        llamaCloudApiKey: payload.llamaCloudApiKey,
        openRouterApiKey: payload.openRouterApiKey,
        app: { id: payload.appId },
      });

      await this.chatAiRepository.save(newChatAi);

      // Log credit usage for project creation
      await this.logCreditUsage(
        newChatAi.id,
        userId.toString(),
        'project_create',
        1,
      );

      return {
        error: false,
        statusCode: HttpStatus.CREATED,
        message: AppMessage.AppCreated('ChatAI'),
      };
    } catch (error) {
      this.logger.error(`Setup ChatAI failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async updateChatAi(userId: number, updateChatAiDto: UpdateChatAiDto) {
    try {
      const chatAi = await this.dataSource
        .getRepository(ChatAi)
        .createQueryBuilder('chatAi')
        .leftJoinAndSelect('chatAi.app', 'app')
        .leftJoinAndSelect('app.user', 'user')
        .where('app.id = :appId', { appId: updateChatAiDto.appId })
        .andWhere('user.id = :userId', { userId })
        .getOne();

      if (!chatAi) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }

      // Update ChatAI fields
      if (updateChatAiDto.name) chatAi.name = updateChatAiDto.name;
      if (updateChatAiDto.description !== undefined)
        chatAi.description = updateChatAiDto.description;
      if (updateChatAiDto.llamaCloudApiKey !== undefined)
        chatAi.llamaCloudApiKey = updateChatAiDto.llamaCloudApiKey;
      if (updateChatAiDto.openRouterApiKey !== undefined)
        chatAi.openRouterApiKey = updateChatAiDto.openRouterApiKey;

      await this.chatAiRepository.save(chatAi);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.AppUpdated('ChatAI'),
      };
    } catch (error) {
      this.logger.error(`Update ChatAI failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async updateChatAiSettings(
    userId: number,
    updateChatAiSettingDto: UpdateChatAiSettingDto,
  ) {
    try {
      const chatAi = await this.dataSource
        .getRepository(ChatAi)
        .createQueryBuilder('chatAi')
        .leftJoinAndSelect('chatAi.app', 'app')
        .leftJoinAndSelect('app.user', 'user')
        .where('app.id = :appId', { appId: updateChatAiSettingDto.appId })
        .andWhere('user.id = :userId', { userId })
        .getOne();

      if (!chatAi) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }

      // Update settings
      if (updateChatAiSettingDto.notificationsEnabled !== undefined)
        chatAi.notificationsEnabled = updateChatAiSettingDto.notificationsEnabled;
      if (updateChatAiSettingDto.notificationEmail !== undefined)
        chatAi.notificationEmail = updateChatAiSettingDto.notificationEmail;
      if (updateChatAiSettingDto.credits !== undefined)
        chatAi.credits = updateChatAiSettingDto.credits;
      if (updateChatAiSettingDto.subscriptionStatus !== undefined)
        chatAi.subscriptionStatus = updateChatAiSettingDto.subscriptionStatus;

      await this.chatAiRepository.save(chatAi);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.AppUpdated('ChatAI Settings'),
      };
    } catch (error) {
      this.logger.error(`Update ChatAI settings failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getSingleChatAi(userId: number, query: FetchSingleChatAiDto) {
    try {
      const chatAi = await this.dataSource
        .getRepository(ChatAi)
        .createQueryBuilder('chatAi')
        .leftJoinAndSelect('chatAi.app', 'app')
        .leftJoinAndSelect('app.user', 'user')
        .leftJoinAndSelect('chatAi.documents', 'documents')
        .where('app.id = :appId', { appId: query.appId })
        .andWhere('user.id = :userId', { userId })
        .getOne();

      if (!chatAi) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }

      return {
        error: false,
        statusCode: HttpStatus.OK,
        result: chatAi,
      };
    } catch (error) {
      this.logger.error(`Get single ChatAI failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getAllChatAis(userId: number, query: FetchChatAisDto) {
    try {
      const queryBuilder = this.dataSource
        .getRepository(ChatAi)
        .createQueryBuilder('chatAi')
        .leftJoinAndSelect('chatAi.app', 'app')
        .leftJoinAndSelect('app.user', 'user')
        .where('user.id = :userId', { userId });

      if (query.search) {
        queryBuilder.andWhere('chatAi.name ILIKE :search', {
          search: `%${query.search}%`,
        });
      }

      const totalCount = await queryBuilder.getCount();
      const result = await queryBuilder
        .skip((query.page - 1) * query.limit)
        .take(query.limit)
        .orderBy('chatAi.createdAt', 'DESC')
        .getMany();

      return {
        error: false,
        statusCode: HttpStatus.OK,
        result: paginate(result, totalCount, query.page, query.limit),
      };
    } catch (error) {
      this.logger.error(`Get all ChatAIs failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async removeChatAi(userId: number, removeChatAiDto: RemoveChatAiDto) {
    try {
      const chatAi = await this.dataSource
        .getRepository(ChatAi)
        .createQueryBuilder('chatAi')
        .leftJoinAndSelect('chatAi.app', 'app')
        .leftJoinAndSelect('app.user', 'user')
        .where('app.id = :appId', { appId: removeChatAiDto.appId })
        .andWhere('user.id = :userId', { userId })
        .getOne();

      if (!chatAi) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }

      await this.chatAiRepository.remove(chatAi);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.AppDeleted('ChatAI'),
      };
    } catch (error) {
      this.logger.error(`Remove ChatAI failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  // ==================== Document Management (Placeholder) ====================

  // async uploadDocument(userId: number, file: Express.Multer.File, createDocumentDto: CreateDocumentDto) {
  //   // TODO: Implement document upload logic
  //   return {
  //     error: false,
  //     statusCode: HttpStatus.CREATED,
  //     message: 'Document upload functionality coming soon',
  //   };
  // }

  // async getDocuments(userId: number, query: FetchSingleChatAiDto) {
  //   // TODO: Implement get documents logic
  //   return {
  //     error: false,
  //     statusCode: HttpStatus.OK,
  //     result: [],
  //   };
  // }

  // async updateDocument(userId: number, updateDocumentDto: UpdateDocumentDto) {
  //   // TODO: Implement update document logic
  //   return {
  //     error: false,
  //     statusCode: HttpStatus.OK,
  //     message: 'Document update functionality coming soon',
  //   };
  // }

  // // ==================== Chat Management (Placeholder) ====================

  // async chat(userId: number, chatQueryDto: ChatQueryDto) {
  //   // TODO: Implement chat logic
  //   return {
  //     error: false,
  //     statusCode: HttpStatus.OK,
  //     message: 'Chat functionality coming soon',
  //   };
  // }

  // async getChatHistory(userId: number, query: FetchSingleChatAiDto) {
  //   // TODO: Implement get chat history logic
  //   return {
  //     error: false,
  //     statusCode: HttpStatus.OK,
  //     result: [],
  //   };
  // }

  // async createMessage(userId: number, createChatMessageDto: CreateChatMessageDto) {
  //   // TODO: Implement create message logic
  //   return {
  //     error: false,
  //     statusCode: HttpStatus.CREATED,
  //     message: 'Message creation functionality coming soon',
  //   };
  // }

  // // ==================== Transaction & Credit Management (Placeholder) ====================

  // async getAllTransactions(userId: number, query: GetChatAiTransactionDto) {
  //   // TODO: Implement get transactions logic
  //   return {
  //     error: false,
  //     statusCode: HttpStatus.OK,
  //     result: [],
  //   };
  // }

  // async getCreditUsage(userId: number, query: FetchSingleChatAiDto) {
  //   // TODO: Implement get credit usage logic
  //   return {
  //     error: false,
  //     statusCode: HttpStatus.OK,
  //     result: [],
  //   };
  // }

  // // ==================== Helper Methods ====================

  private async logCreditUsage(
    projectId: string,
    userId: string,
    actionType: string,
    creditsUsed: number,
  ) {
    const creditUsage = this.creditUsageRepository.create({
      projectId,
      userId,
      actionType,
      creditsUsed,
    });
    await this.creditUsageRepository.save(creditUsage);
  }
}
